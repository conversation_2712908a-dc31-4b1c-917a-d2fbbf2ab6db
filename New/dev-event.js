#!/usr/bin/env node

/**
 * DermaCare Socket Event to Webhook Converter
 * 
 * This development utility connects to the CC socket server and converts
 * socket events to webhook format for testing the new webhook-based system.
 * 
 * Features:
 * - Connects to CC socket server using credentials from v3Integration
 * - Listens to all socket events that were handled in v3Integration/start/Socket.ts
 * - Converts socket events to webhook format compatible with New/src/webhook/ccHandler.ts
 * - Sends converted events to local dev server
 * - Comprehensive error handling and logging
 * - Configurable dev server URL
 */

import io from 'socket.io-client';
import fetch from 'node-fetch';

// Configuration - Update these values with your actual CC socket credentials
const CONFIG = {
  // Dev Server Configuration
  devServerUrl: 'http://localhost:8787',
  webhookEndpoint: '/cc/webhook/',
  authToken: 'dermacare-secure-token-2024',
  retryAttempts: 3,
  retryDelay: 1000,
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'

  // CC Socket Credentials - UPDATE THESE WITH YOUR ACTUAL VALUES
  ccCredentials: {
    name: 'CC <PERSON>',
    CCSocketUrl: 'wss://ccdemo.clinicore.eu', // Replace with your actual socket URL
    CCSocketToken: '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************', // Replace with your actual socket token
    CCApiUrl: 'https://ccdemo.clinicore.eu/api/v1' // Replace with your actual API URL
  }
};

// Socket event to webhook event mapping
const EVENT_MAPPING = {
  'mobimed:App\\Events\\EntityWasCreated': 'EntityWasCreated',
  'mobimed:App\\Events\\EntityWasUpdated': 'EntityWasUpdated', 
  'mobimed:App\\Events\\EntityWasDeleted': 'EntityWasDeleted',
  'mobimed:App\\Events\\AppointmentWasCreated': 'AppointmentWasCreated'
};

// Model type mapping
const MODEL_MAPPING = {
  'patient': 'Patient',
  'appointment': 'Appointment',
  'invoice': 'Invoice',
  'payment': 'Payment',
  'service': 'Service'
};

/**
 * Logger utility with different log levels
 */
class Logger {
  static log(level, message, data = null) {
    const levels = ['debug', 'info', 'warn', 'error'];
    const configLevel = levels.indexOf(CONFIG.logLevel);
    const messageLevel = levels.indexOf(level);
    
    if (messageLevel >= configLevel) {
      const timestamp = new Date().toISOString();
      const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
      
      if (data) {
        console.log(`${prefix} ${message}`, JSON.stringify(data, null, 2));
      } else {
        console.log(`${prefix} ${message}`);
      }
    }
  }

  static debug(message, data) { this.log('debug', message, data); }
  static info(message, data) { this.log('info', message, data); }
  static warn(message, data) { this.log('warn', message, data); }
  static error(message, data) { this.log('error', message, data); }
}

/**
 * Load CC socket credentials from hardcoded configuration
 *
 * To use this tool, update the CONFIG.ccCredentials object above with your actual values:
 * - CCSocketUrl: WebSocket URL for CC socket server (e.g., wss://your-domain.com/socket.io)
 * - CCSocketToken: Authentication token for socket connection
 * - name: Name identifier for the connection
 * - CCApiUrl: API URL for reference
 */
async function loadOAuthCredentials() {
  try {
    const credentials = CONFIG.ccCredentials;

    if (!credentials.CCSocketUrl || !credentials.CCSocketToken) {
      throw new Error('CC Socket credentials not configured');
    }

    if (credentials.CCSocketUrl.includes('your-cc-domain.com') ||
        credentials.CCSocketToken === 'your-socket-authentication-token') {
      Logger.warn('⚠️  Using placeholder credentials - please update with actual values');
    }

    return [credentials];
  } catch (error) {
    Logger.error('Failed to load CC socket credentials:', error.message);
    throw error;
  }
}

/**
 * Convert socket event data to webhook format
 */
function convertSocketEventToWebhook(socketEvent, eventData) {
  const webhookEvent = EVENT_MAPPING[socketEvent];
  if (!webhookEvent) {
    throw new Error(`Unknown socket event: ${socketEvent}`);
  }

  const model = MODEL_MAPPING[eventData.type];
  if (!model) {
    throw new Error(`Unknown model type: ${eventData.type}`);
  }

  return {
    event: webhookEvent,
    model: model,
    id: eventData.payload?.id || eventData.id || Math.floor(Math.random() * 1000000),
    payload: eventData.payload || eventData,
    timestamp: new Date().toISOString()
  };
}

/**
 * Send webhook to dev server with retry logic
 */
async function sendWebhook(webhookData, attempt = 1) {
  const url = `${CONFIG.devServerUrl}${CONFIG.webhookEndpoint}`;
  
  try {
    Logger.debug(`Sending webhook (attempt ${attempt}):`, webhookData);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${CONFIG.authToken}`
      },
      body: JSON.stringify(webhookData)
    });

    const responseData = await response.json();
    
    if (response.ok) {
      Logger.info(`✅ Webhook sent successfully:`, {
        event: webhookData.event,
        model: webhookData.model,
        id: webhookData.id,
        response: responseData
      });
      return true;
    } else {
      throw new Error(`HTTP ${response.status}: ${JSON.stringify(responseData)}`);
    }
  } catch (error) {
    Logger.error(`❌ Failed to send webhook (attempt ${attempt}):`, {
      error: error.message,
      webhook: webhookData
    });

    if (attempt < CONFIG.retryAttempts) {
      Logger.info(`🔄 Retrying in ${CONFIG.retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay));
      return sendWebhook(webhookData, attempt + 1);
    }
    
    return false;
  }
}

/**
 * Process socket event and convert to webhook
 */
async function processSocketEvent(socketEvent, eventData) {
  try {
    Logger.info(`🎯 Socket event received: ${socketEvent}`, {
      type: eventData.type,
      id: eventData.payload?.id || eventData.id
    });

    // Convert to webhook format
    const webhookData = convertSocketEventToWebhook(socketEvent, eventData);
    
    // Send to dev server
    await sendWebhook(webhookData);
    
  } catch (error) {
    Logger.error(`Failed to process socket event:`, {
      socketEvent,
      eventData,
      error: error.message
    });
  }
}

/**
 * Connect to CC socket server
 */
async function connectToSocket(auth) {
  return new Promise((resolve, reject) => {
    Logger.info(`🔌 Connecting to ${auth.name} socket server...`);
    
    const socket = io.connect(auth.CCSocketUrl, {
      query: 'access_token=' + auth.CCSocketToken,
      transport: ['websocket'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 2000
    });

    socket.on('connect', () => {
      Logger.info(`✅ Connected to socket server for ${auth.name}`);
      Logger.info(`🎧 Listening for socket events from ${auth.CCApiUrl}`);
      resolve(socket);
    });

    socket.on('error', (error) => {
      Logger.error(`❌ Socket connection error for ${auth.name}:`, error);
      reject(error);
    });

    socket.on('disconnect', (reason) => {
      Logger.warn(`🔌 Socket disconnected for ${auth.name}:`, reason);
    });

    socket.on('reconnect', (attemptNumber) => {
      Logger.info(`🔄 Socket reconnected for ${auth.name} after ${attemptNumber} attempts`);
    });

    // Listen to all the events from the original Socket.ts
    
    // EntityWasCreated events
    socket.on('mobimed:App\\Events\\EntityWasCreated', async (data) => {
      await processSocketEvent('mobimed:App\\Events\\EntityWasCreated', data);
    });

    // AppointmentWasCreated events  
    socket.on('mobimed:App\\Events\\AppointmentWasCreated', async (data) => {
      await processSocketEvent('mobimed:App\\Events\\AppointmentWasCreated', data);
    });

    // EntityWasUpdated events
    socket.on('mobimed:App\\Events\\EntityWasUpdated', async (data) => {
      await processSocketEvent('mobimed:App\\Events\\EntityWasUpdated', data);
    });

    // EntityWasDeleted events
    socket.on('mobimed:App\\Events\\EntityWasDeleted', async (data) => {
      await processSocketEvent('mobimed:App\\Events\\EntityWasDeleted', data);
    });

    // Note: socket.io-client v1.x doesn't have onAny method
    // All events are handled explicitly above
  });
}

/**
 * Main function to start the socket event listener
 */
async function main() {
  Logger.info('🚀 Starting DermaCare Socket Event to Webhook Converter...');
  Logger.info('📋 Dev Server URL:', CONFIG.devServerUrl);
  Logger.info('📋 Log Level:', CONFIG.logLevel);

  try {
    // Load CC socket credentials
    const auths = await loadOAuthCredentials();
    
    if (auths.length === 0) {
      throw new Error('No authentication credentials found');
    }

    // Connect to each socket server
    const connections = [];
    for (const auth of auths) {
      try {
        const socket = await connectToSocket(auth);
        connections.push({ auth, socket });
      } catch (error) {
        Logger.error(`Failed to connect to ${auth.name}:`, error.message);
      }
    }

    if (connections.length === 0) {
      throw new Error('No socket connections established');
    }

    Logger.info(`✅ Successfully connected to ${connections.length} socket server(s)`);
    Logger.info('🎧 Listening for events... Press Ctrl+C to stop');

    // Keep the process running
    process.on('SIGINT', () => {
      Logger.info('🛑 Shutting down...');
      connections.forEach(({ socket }) => socket.disconnect());
      process.exit(0);
    });

  } catch (error) {
    Logger.error('💥 Fatal error:', error.message);
    process.exit(1);
  }
}

// Start the application
main().catch((error) => {
  console.error('💥 Unhandled error:', error);
  process.exit(1);
});
